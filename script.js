// 全局变量
let selectedFile = null;
let workbookData = null;
let currentWorkbookData = null; // 存储当前工作簿数据
let dateDistribution = null; // 存储日期分布数据
let selectedDates = new Set(); // 存储选中的日期
let currentCalendarDate = new Date(); // 当前显示的月份

// DOM元素引用
const fileInput = document.getElementById('fileInput');
const uploadArea = document.getElementById('uploadArea');
const fileInfo = document.getElementById('fileInfo');
const convertBtn = document.getElementById('convertBtn');
const progressSection = document.getElementById('progressSection');
const progressFill = document.getElementById('progressFill');
const progressText = document.getElementById('progressText');
const messageArea = document.getElementById('messageArea');

// 月视图相关元素
const monthViewSection = document.getElementById('monthViewSection');
const totalRecords = document.getElementById('totalRecords');
const dateRange = document.getElementById('dateRange');
const selectedCount = document.getElementById('selectedCount');
const currentMonth = document.getElementById('currentMonth');
const calendarContainer = document.getElementById('calendarContainer');
const selectedDatesList = document.getElementById('selectedDatesList');
const prevMonthBtn = document.getElementById('prevMonth');
const nextMonthBtn = document.getElementById('nextMonth');
const clearSelectionBtn = document.getElementById('clearSelection');
const previewDataBtn = document.getElementById('previewData');
const exportFilteredBtn = document.getElementById('exportFiltered');

// 初始化事件监听器
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
});

function initializeEventListeners() {
    // 文件选择事件
    fileInput.addEventListener('change', handleFileSelect);

    // 拖拽事件
    uploadArea.addEventListener('dragover', handleDragOver);
    uploadArea.addEventListener('dragleave', handleDragLeave);
    uploadArea.addEventListener('drop', handleFileDrop);

    // 转换按钮事件
    convertBtn.addEventListener('click', handleConvert);

    // 月视图事件监听器
    if (prevMonthBtn) prevMonthBtn.addEventListener('click', showPreviousMonth);
    if (nextMonthBtn) nextMonthBtn.addEventListener('click', showNextMonth);
    if (clearSelectionBtn) clearSelectionBtn.addEventListener('click', clearDateSelection);
    if (previewDataBtn) previewDataBtn.addEventListener('click', previewFilteredData);
    if (exportFilteredBtn) exportFilteredBtn.addEventListener('click', exportFilteredData);
}

// 处理文件选择
function handleFileSelect(event) {
    const file = event.target.files[0];
    if (file) {
        validateAndProcessFile(file);
    }
}

// 处理拖拽悬停
function handleDragOver(event) {
    event.preventDefault();
    uploadArea.classList.add('dragover');
}

// 处理拖拽离开
function handleDragLeave(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
}

// 处理文件拖拽放置
function handleFileDrop(event) {
    event.preventDefault();
    uploadArea.classList.remove('dragover');
    
    const files = event.dataTransfer.files;
    if (files.length > 0) {
        validateAndProcessFile(files[0]);
    }
}

// 验证和处理文件
async function validateAndProcessFile(file) {
    // 验证文件类型
    if (!file.name.toLowerCase().endsWith('.xlsx')) {
        showMessage('请选择 .xlsx 格式的Excel文件', 'error');
        return;
    }

    // 验证文件大小 (限制为10MB)
    if (file.size > 10 * 1024 * 1024) {
        showMessage('文件大小不能超过10MB', 'error');
        return;
    }

    selectedFile = file;
    displayFileInfo(file);

    // 自动开始处理文件
    showMessage('文件选择成功，正在自动加载...', 'info');
    await autoProcessFile();
}

// 显示文件信息
function displayFileInfo(file) {
    document.getElementById('fileName').textContent = file.name;
    document.getElementById('fileSize').textContent = formatFileSize(file.size);
    document.getElementById('fileDate').textContent = new Date(file.lastModified).toLocaleString('zh-CN');
    
    fileInfo.style.display = 'block';
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 自动处理文件（新函数）
async function autoProcessFile() {
    if (!selectedFile) {
        showMessage('请先选择文件', 'error');
        return;
    }

    try {
        // 隐藏转换按钮，因为现在是自动处理
        convertBtn.style.display = 'none';

        // 显示进度
        showProgress(true);
        updateProgress(10, '正在读取文件...');

        // 读取文件
        const workbook = await readExcelFileWithExcelJS(selectedFile);
        updateProgress(50, '正在处理数据...');

        // 处理数据
        const processedData = processWorkbookDataFromExcelJS(workbook);
        currentWorkbookData = processedData; // 保存处理后的数据
        updateProgress(80, '正在分析日期数据...');

        // 分析日期分布
        analyzeDateDistribution(processedData);
        updateProgress(100, '数据加载完成！');

        showMessage('文件加载成功！请在下方月视图中选择需要导出的日期。', 'success');

        // 显示月视图
        showMonthView();

    } catch (error) {
        console.error('文件处理过程中发生错误:', error);
        showMessage('文件处理失败: ' + error.message, 'error');
        // 如果处理失败，重新显示转换按钮
        convertBtn.style.display = 'block';
    } finally {
        // 隐藏进度条
        setTimeout(() => {
            showProgress(false);
        }, 1000);
    }
}

// 处理转换（保留原函数但修改为仅用于重新处理）
async function handleConvert() {
    await autoProcessFile();
}

// 读取文件为ArrayBuffer
function readFileAsArrayBuffer(file) {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (e) => resolve(e.target.result);
        reader.onerror = (e) => reject(new Error('文件读取失败'));
        reader.readAsArrayBuffer(file);
    });
}

// 使用ExcelJS读取Excel文件
async function readExcelFileWithExcelJS(file) {
    const arrayBuffer = await readFileAsArrayBuffer(file);
    const workbook = new ExcelJS.Workbook();
    await workbook.xlsx.load(arrayBuffer);
    return workbook;
}

// 使用ExcelJS处理工作簿数据
function processWorkbookDataFromExcelJS(workbook) {
    // 获取第一个工作表
    const worksheet = workbook.worksheets[0];
    if (!worksheet) {
        throw new Error('工作簿中没有找到工作表');
    }

    // 提取数据 - 特别处理合并单元格
    const data = [];
    worksheet.eachRow((row, rowNumber) => {
        const rowData = [];

        // 获取行的实际列数
        const actualColumnCount = row.actualCellCount || row.cellCount;

        // 遍历所有可能的列
        for (let colNumber = 1; colNumber <= Math.max(actualColumnCount, 10); colNumber++) {
            const cell = row.getCell(colNumber);

            // 处理日期类型
            if (cell.type === ExcelJS.ValueType.Date) {
                rowData[colNumber - 1] = cell.value;
            } else {
                rowData[colNumber - 1] = cell.value;
            }
        }

        data.push(rowData);
    });

    if (data.length === 0) {
        throw new Error('工作表中没有数据');
    }

    // 智能检测表头位置
    let headers = null;
    let dataStartRow = 1;

    console.log('原始数据前3行:', data.slice(0, 3));

    // 检查第一行是否是合并的标题行
    if (data[0]) {
        const firstRowNonEmptyCells = data[0].filter(cell =>
            cell !== null && cell !== undefined && String(cell).trim() !== ''
        );

        console.log('第一行非空单元格:', firstRowNonEmptyCells);

        // 检查第一行是否所有非空单元格都是相同的且包含"科陆流水线"
        // 这表明是合并单元格的主标题行
        if (firstRowNonEmptyCells.length > 0) {
            const firstCellValue = String(firstRowNonEmptyCells[0]).trim();
            const allSame = firstRowNonEmptyCells.every(cell =>
                String(cell).trim() === firstCellValue
            );

            if (allSame && firstCellValue.includes('科陆流水线')) {
                // 第一行是标题行，第二行是列标题
                headers = data[1];
                dataStartRow = 2;
                console.log('检测到主标题行（合并单元格），使用第二行作为列标题');
            } else {
                // 第一行就是列标题
                headers = data[0];
                dataStartRow = 1;
                console.log('第一行作为列标题');
            }
        } else {
            throw new Error('第一行没有有效数据');
        }
    } else {
        throw new Error('数据为空');
    }

    if (!headers || headers.length === 0) {
        throw new Error('无法读取表头信息');
    }

    // 清理和过滤列标题
    headers = headers.map(header => String(header || '').trim()).filter(h => h);

    if (headers.length === 0) {
        throw new Error('未找到有效的列标题');
    }

    console.log('检测到的列标题:', headers);
    console.log('数据开始行:', dataStartRow);

    // 过滤掉'记录人'列并重新排列列顺序
    const filteredHeaders = [];
    const columnIndexMap = new Map();

    // 找到关键列的索引
    let dateColumnIndex = -1;
    let failureColumnIndex = -1;

    headers.forEach((header, index) => {
        if (!header.includes('记录人')) {
            const headerStr = String(header).toLowerCase();
            // 检查是否是日期列
            if (headerStr.includes('日期') || headerStr.includes('date') || headerStr.includes('时间')) {
                dateColumnIndex = filteredHeaders.length;
            }
            // 检查是否是故障处理情况列
            if (headerStr.includes('故障')) {
                failureColumnIndex = filteredHeaders.length;
            }

            columnIndexMap.set(filteredHeaders.length, index);
            filteredHeaders.push(header);
        }
    });

    // 在日期列和故障处理情况列之间插入维护保养情况列
    let maintenanceInsertIndex = filteredHeaders.length; // 默认插入到末尾

    if (dateColumnIndex !== -1 && failureColumnIndex !== -1) {
        // 如果找到了日期列和故障处理情况列，在它们之间插入
        maintenanceInsertIndex = Math.max(dateColumnIndex + 1, failureColumnIndex);
    } else if (dateColumnIndex !== -1) {
        // 如果只找到日期列，在日期列后插入
        maintenanceInsertIndex = dateColumnIndex + 1;
    }

    // 插入维护保养情况列
    filteredHeaders.splice(maintenanceInsertIndex, 0, '维护保养情况');

    // 更新columnIndexMap以适应新插入的列
    const newColumnIndexMap = new Map();
    let filteredIndex = 0; // 用于跟踪过滤后的原始列索引
    filteredHeaders.forEach((header, index) => {
        if (header === '维护保养情况') {
            // 维护保养情况列是新添加的，没有对应的原始列
            newColumnIndexMap.set(index, -1);
        } else {
            // 获取当前过滤后列对应的原始列索引
            newColumnIndexMap.set(index, columnIndexMap.get(filteredIndex));
            filteredIndex++;
        }
    });

    // 添加备注列到末尾
    filteredHeaders.push('备注');
    newColumnIndexMap.set(filteredHeaders.length - 1, -2); // -2表示备注列

    console.log('处理后的列标题:', filteredHeaders);

    // 处理数据行 - 使用正确的数据开始行
    const dataRows = data.slice(dataStartRow).filter(row =>
        row.some(cell => cell !== null && cell !== undefined && String(cell).trim() !== '')
    );

    console.log('有效数据行数:', dataRows.length);

    if (dataRows.length === 0) {
        throw new Error('没有找到有效的数据行');
    }

    // 找到日期列的索引
    const finalDateColumnIndex = findDateColumnIndex(filteredHeaders);
    console.log('日期列索引:', finalDateColumnIndex);

    // 转换数据行
    const processedRows = dataRows.map(row => {
        const newRow = [];

        // 按照新的列顺序填充数据
        filteredHeaders.forEach((header, newColIndex) => {
            const originalColIndex = newColumnIndexMap.get(newColIndex);
            let cellValue = '';

            if (originalColIndex === -1) {
                // 维护保养情况列 - 新添加的列，初始为空
                cellValue = '';
            } else if (originalColIndex === -2) {
                // 备注列 - 新添加的列，默认值为"已解决"
                cellValue = '已解决';
            } else {
                // 原有列数据
                cellValue = row[originalColIndex] || '';

                // 如果是日期列，保持日期类型
                if (newColIndex === finalDateColumnIndex && cellValue) {
                    cellValue = formatDateValue(cellValue);
                }
            }

            newRow.push(cellValue);
        });

        return newRow;
    });

    // 按日期分组数据
    const groupedData = groupDataByDate(processedRows, finalDateColumnIndex, filteredHeaders);

    console.log('分组后的数据行数:', groupedData.rows.length);
    console.log('合并信息:', groupedData.mergeInfo);

    // 调试：统计最终数据中的"空仓运维"数量
    const emptyColumnIndex = findColumnIndex(filteredHeaders, ['空仓', '空仓？']);
    if (emptyColumnIndex !== -1) {
        const emptyMaintenanceCount = groupedData.rows.filter(row =>
            row[emptyColumnIndex] && String(row[emptyColumnIndex]).trim() === '空仓运维'
        ).length;
        console.log(`=== 数据处理完成统计 ===`);
        console.log(`最终返回的数据中"空仓运维"数量: ${emptyMaintenanceCount}`);
        console.log(`空仓列索引: ${emptyColumnIndex}`);
    }

    return {
        headers: filteredHeaders,
        rows: groupedData.rows,
        mergeInfo: groupedData.mergeInfo
    };
}

// 处理工作簿数据
function processWorkbookData(workbook) {
    // 获取第一个工作表
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];

    // 将工作表转换为JSON数据
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

    if (jsonData.length === 0) {
        throw new Error('Excel文件为空或格式不正确');
    }

    // 智能检测表头位置
    let headers = null;
    let dataStartRow = 1;

    // 检查第一行是否是合并的标题行（只有一列且包含"科陆流水线"）
    if (jsonData[0] && jsonData[0].length === 1 &&
        String(jsonData[0][0]).includes('科陆流水线')) {
        // 第一行是标题行，第二行是列标题
        headers = jsonData[1];
        dataStartRow = 2;
    } else {
        // 第一行就是列标题
        headers = jsonData[0];
        dataStartRow = 1;
    }

    if (!headers || headers.length === 0) {
        throw new Error('无法读取表头信息');
    }

    // 过滤掉'记录人'列并重新排列列顺序
    const filteredHeaders = [];
    const columnIndexMap = [];

    // 找到关键列的索引
    let dateColIndex = -1;
    let failureColIndex = -1;

    headers.forEach((header, index) => {
        if (header !== '记录人') {
            const headerStr = String(header).toLowerCase();
            // 检查是否是日期列
            if (headerStr.includes('日期') || headerStr.includes('date') || headerStr.includes('时间')) {
                dateColIndex = filteredHeaders.length;
            }
            // 检查是否是故障处理情况列
            if (headerStr.includes('故障')) {
                failureColIndex = filteredHeaders.length;
            }

            filteredHeaders.push(header);
            columnIndexMap.push(index);
        }
    });

    // 在日期列和故障处理情况列之间插入维护保养情况列
    let maintenanceInsertPos = filteredHeaders.length; // 默认插入到末尾

    if (dateColIndex !== -1 && failureColIndex !== -1) {
        // 如果找到了日期列和故障处理情况列，在它们之间插入
        maintenanceInsertPos = Math.max(dateColIndex + 1, failureColIndex);
    } else if (dateColIndex !== -1) {
        // 如果只找到日期列，在日期列后插入
        maintenanceInsertPos = dateColIndex + 1;
    }

    // 插入维护保养情况列
    filteredHeaders.splice(maintenanceInsertPos, 0, '维护保养情况');
    columnIndexMap.splice(maintenanceInsertPos, 0, -1); // -1表示新添加的列

    // 添加备注列到末尾
    filteredHeaders.push('备注');
    columnIndexMap.push(-2); // -2表示备注列

    // 找到日期列的索引
    const dateColumnIndex = findDateColumnIndex(filteredHeaders);
    if (dateColumnIndex === -1) {
        throw new Error('未找到日期列，请确保Excel文件包含日期相关的列');
    }

    // 处理数据行
    const rawRows = [];
    for (let i = dataStartRow; i < jsonData.length; i++) {
        const row = jsonData[i];
        if (row && row.length > 0 && row.some(cell => cell !== undefined && cell !== '')) {
            const newRow = [];

            // 按照新的列顺序填充数据
            columnIndexMap.forEach((colIndex, newColIndex) => {
                let cellValue = '';

                if (colIndex === -1) {
                    // 维护保养情况列 - 新添加的列，初始为空
                    cellValue = '';
                } else if (colIndex === -2) {
                    // 备注列 - 新添加的列，默认值为"已解决"
                    cellValue = '已解决';
                } else {
                    // 原有列数据
                    cellValue = row[colIndex] || '';

                    // 如果是日期列，保持日期类型
                    if (newColIndex === dateColumnIndex && cellValue) {
                        cellValue = formatDateValue(cellValue);
                    }
                }

                newRow.push(cellValue);
            });

            rawRows.push(newRow);
        }
    }

    // 按日期分组并处理合并逻辑
    const groupedData = groupDataByDate(rawRows, dateColumnIndex, filteredHeaders);

    // 数据处理完成

    return {
        headers: filteredHeaders,
        rows: groupedData.rows,
        mergeInfo: groupedData.mergeInfo
    };
}

// 查找日期列的索引
function findDateColumnIndex(headers) {
    const dateKeywords = ['日期', 'date', '时间', 'time', '日', '月', '年'];

    for (let i = 0; i < headers.length; i++) {
        const header = String(headers[i]).toLowerCase();
        if (dateKeywords.some(keyword => header.includes(keyword))) {
            return i;
        }
    }

    return -1; // 未找到日期列
}

// 按日期分组数据并处理合并逻辑
function groupDataByDate(rows, dateColumnIndex, headers) {
    if (rows.length === 0) {
        return { rows: [], mergeInfo: [] };
    }

    // 按日期分组
    const dateGroups = new Map();

    rows.forEach((row, index) => {
        const dateGroupKey = getDateGroupKey(row[dateColumnIndex]);

        if (!dateGroups.has(dateGroupKey)) {
            dateGroups.set(dateGroupKey, []);
        }
        dateGroups.get(dateGroupKey).push({ row, originalIndex: index });
    });

    // 处理分组数据并应用合并逻辑
    const processedRows = [];
    const mergeInfo = [];

    // 找到关键列的索引
    const maintenanceColumnIndex = headers.indexOf('维护保养情况');
    const emptyColumnIndex = findColumnIndex(headers, ['空仓', '空仓？']);

    dateGroups.forEach((group) => {
        const groupStartRow = processedRows.length;

        group.forEach((item, groupIndex) => {
            const row = [...item.row];

            // 处理日期列合并：保持原始日期值，但在Excel中会通过合并单元格来显示
            // 不要清空日期值，让Excel的合并单元格功能来处理显示

            // 处理维护保养情况列合并：只在第一行显示
            if (groupIndex > 0 && maintenanceColumnIndex !== -1) {
                row[maintenanceColumnIndex] = '';
            }
            processedRows.push(row);
        });

        // 记录合并信息
        if (group.length > 1) {
            // 日期列合并
            mergeInfo.push({
                startRow: groupStartRow,
                endRow: groupStartRow + group.length - 1,
                startCol: dateColumnIndex,
                endCol: dateColumnIndex,
                type: 'date'
            });

            // 维护保养情况列合并
            if (maintenanceColumnIndex !== -1) {
                mergeInfo.push({
                    startRow: groupStartRow,
                    endRow: groupStartRow + group.length - 1,
                    startCol: maintenanceColumnIndex,
                    endCol: maintenanceColumnIndex,
                    type: 'maintenance'
                });
            }

            // 处理空仓列的条件合并
            if (emptyColumnIndex !== -1) {
                processEmptyColumnMerge(processedRows, groupStartRow, group.length, emptyColumnIndex, mergeInfo);
            }
        }
    });

    // 调试：统计分组处理后的"空仓运维"数量
    if (emptyColumnIndex !== -1) {
        const emptyMaintenanceCount = processedRows.filter(row =>
            row[emptyColumnIndex] && String(row[emptyColumnIndex]).trim() === '空仓运维'
        ).length;
        console.log(`=== 分组处理完成统计 ===`);
        console.log(`分组处理后"空仓运维"数量: ${emptyMaintenanceCount}`);
    }

    return { rows: processedRows, mergeInfo };
}

// 查找列索引的通用函数
function findColumnIndex(headers, keywords) {
    for (let i = 0; i < headers.length; i++) {
        const header = String(headers[i]).toLowerCase();
        if (keywords.some(keyword => header.includes(keyword.toLowerCase()))) {
            return i;
        }
    }
    return -1;
}

// 格式化日期值 - 返回Date对象用于分组，保持Excel中的日期类型
function formatDateValue(dateValue) {
    if (!dateValue) return null;

    console.log('formatDateValue input:', dateValue, 'type:', typeof dateValue);

    // 如果是Excel日期序列号，使用SheetJS的转换函数
    if (typeof dateValue === 'number' && dateValue > 1) {
        try {
            // 使用SheetJS的日期转换函数，它正确处理Excel的日期系统
            const jsDate = XLSX.SSF.parse_date_code(dateValue);
            if (jsDate && jsDate.y && jsDate.m && jsDate.d) {
                // 创建只包含日期部分的Date对象（时间设为00:00:00）
                const date = new Date(jsDate.y, jsDate.m - 1, jsDate.d, 0, 0, 0, 0);
                console.log('Excel serial number converted:', dateValue, '->', date);
                return date;
            }
        } catch (error) {
            console.warn('Excel date conversion failed, trying manual conversion:', error);
        }

        // 备用转换方法：Excel日期序列号转换
        // Excel的日期序列号1对应1900年1月1日，但需要考虑Excel的1900年闰年bug
        try {
            // 更准确的Excel日期转换
            // Excel日期系统：1900年1月1日 = 1，但Excel错误地认为1900年是闰年
            let excelDate = dateValue;

            // 处理Excel的1900年闰年bug：如果日期 >= 60（1900年3月1日），需要减1
            if (excelDate >= 60) {
                excelDate = excelDate - 1;
            }

            // 计算从1900年1月1日开始的天数
            const baseDate = new Date(1900, 0, 1); // 1900年1月1日
            const resultDate = new Date(baseDate.getTime() + (excelDate - 1) * 86400 * 1000);

            // 确保只包含日期部分
            const finalDate = new Date(resultDate.getFullYear(), resultDate.getMonth(), resultDate.getDate(), 0, 0, 0, 0);
            console.log('Manual Excel date conversion:', dateValue, '->', finalDate);
            return finalDate;
        } catch (error) {
            console.warn('Manual date conversion also failed:', error);
        }
    }

    // 如果是字符串，尝试解析为日期
    if (typeof dateValue === 'string') {
        try {
            const date = new Date(dateValue);
            if (!isNaN(date.getTime())) {
                // 确保只包含日期部分
                const finalDate = new Date(date.getFullYear(), date.getMonth(), date.getDate(), 0, 0, 0, 0);
                console.log('String date converted:', dateValue, '->', finalDate);
                return finalDate;
            }
        } catch (error) {
            console.warn('String date parsing failed:', error);
        }
        // 如果无法解析为日期，返回原字符串
        return dateValue;
    }

    // 如果已经是Date对象，确保只包含日期部分
    if (dateValue instanceof Date) {
        const finalDate = new Date(dateValue.getFullYear(), dateValue.getMonth(), dateValue.getDate(), 0, 0, 0, 0);
        console.log('Date object normalized:', dateValue, '->', finalDate);
        return finalDate;
    }

    console.warn('Unable to convert date value:', dateValue);
    return String(dateValue);
}

// 获取日期的字符串表示用于分组
function getDateGroupKey(dateValue) {
    const formattedDate = formatDateValue(dateValue);

    if (formattedDate instanceof Date) {
        // 使用本地时间而不是UTC时间，避免时区偏差
        const year = formattedDate.getFullYear();
        const month = String(formattedDate.getMonth() + 1).padStart(2, '0');
        const day = String(formattedDate.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    return String(formattedDate || '');
}

// 处理空仓列的条件合并
function processEmptyColumnMerge(rows, startRow, groupSize, emptyColumnIndex, mergeInfo) {
    // 新的合并逻辑：
    // 1. 有数据的行保持不变，不参与合并
    // 2. 只对没有数据的行进行合并

    // 详细调试：记录处理前的状态
    console.log(`=== 空仓列合并开始 ===`);
    console.log(`处理范围: 行${startRow + 1}到${startRow + groupSize}, 空仓列索引: ${emptyColumnIndex}`);

    // 分析组内每行的空仓列数据情况
    const rowAnalysis = [];
    for (let i = 0; i < groupSize; i++) {
        const rowIndex = startRow + i;
        const value = rows[rowIndex][emptyColumnIndex];
        const hasData = value && String(value).trim() !== '';
        rowAnalysis.push({
            rowIndex: rowIndex,
            value: value,
            hasData: hasData,
            valueType: typeof value,
            originalValue: JSON.stringify(value)
        });

        console.log(`  行${rowIndex + 1}: 值="${value}" (类型:${typeof value}, 有数据:${hasData})`);
    }

    // 找出有数据的行和没有数据的行
    const rowsWithData = rowAnalysis.filter(row => row.hasData);
    const rowsWithoutData = rowAnalysis.filter(row => !row.hasData);

    console.log(`空仓列合并分析 - 组内行数: ${groupSize}, 有数据行数: ${rowsWithData.length}, 无数据行数: ${rowsWithoutData.length}`);

    // 详细记录有数据的行
    if (rowsWithData.length > 0) {
        console.log(`有数据的行详情:`);
        rowsWithData.forEach(row => {
            console.log(`  行${row.rowIndex + 1}: "${row.value}"`);
        });
    }

    // 有数据的行保持不变（不做任何处理）
    // 只处理没有数据的行
    if (rowsWithoutData.length > 1) {
        console.log(`开始合并${rowsWithoutData.length}个无数据行...`);

        // 对没有数据的行进行合并：保留第一个无数据行，清空其他无数据行
        const firstEmptyRowIndex = rowsWithoutData[0].rowIndex;

        // 清空其他没有数据的行（除了第一个）
        for (let i = 1; i < rowsWithoutData.length; i++) {
            const rowIndex = rowsWithoutData[i].rowIndex;
            const beforeValue = rows[rowIndex][emptyColumnIndex];
            rows[rowIndex][emptyColumnIndex] = '';
            console.log(`  清空行${rowIndex + 1}: "${beforeValue}" -> ""`);
        }

        // 记录合并信息（只针对无数据行的合并）
        if (rowsWithoutData.length > 1) {
            mergeInfo.push({
                startRow: firstEmptyRowIndex,
                endRow: rowsWithoutData[rowsWithoutData.length - 1].rowIndex,
                startCol: emptyColumnIndex,
                endCol: emptyColumnIndex,
                type: 'empty-only',
                description: `合并了${rowsWithoutData.length}个无数据行，保留了${rowsWithData.length}个有数据行`
            });
        }

        console.log(`空仓列合并完成 - 保留了${rowsWithData.length}个有数据行，合并了${rowsWithoutData.length}个无数据行`);

        // 验证合并后的状态
        console.log(`合并后验证:`);
        for (let i = 0; i < groupSize; i++) {
            const rowIndex = startRow + i;
            const value = rows[rowIndex][emptyColumnIndex];
            const hasData = value && String(value).trim() !== '';
            console.log(`  行${rowIndex + 1}: "${value}" (有数据:${hasData})`);
        }

        console.log(`=== 空仓列合并结束 ===`);
    } else {
        console.log(`空仓列无需合并 - 无数据行数量: ${rowsWithoutData.length}`);
        console.log(`=== 空仓列合并结束 ===`);
    }
}

// 创建格式化的工作簿
function createFormattedWorkbook(data) {
    // 创建新工作簿
    const newWorkbook = XLSX.utils.book_new();

    // 准备数据 - 添加主标题行
    const mainTitle = ['科陆流水线日常运维及故障处理情况'];
    const worksheetData = [
        mainTitle,
        data.headers,
        ...data.rows
    ];

    // 创建工作表
    const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

    // 应用格式化
    applyWorksheetFormatting(worksheet, data);

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(newWorkbook, worksheet, '运维日志');

    return newWorkbook;
}

// 应用工作表格式化
function applyWorksheetFormatting(worksheet, data) {
    const range = XLSX.utils.decode_range(worksheet['!ref']);
    const numCols = data.headers.length;

    // 设置列宽
    const columnWidths = getColumnWidths(data.headers);
    worksheet['!cols'] = columnWidths;

    // 设置行高 - 任务9要求：统一行高25像素
    const rowHeights = [];
    for (let i = 0; i <= range.e.r; i++) {
        rowHeights.push({ hpt: 25 }); // 25像素行高，适合自动换行显示
    }
    worksheet['!rows'] = rowHeights;

    // 合并主标题行
    const titleMerge = {
        s: { r: 0, c: 0 },
        e: { r: 0, c: numCols - 1 }
    };

    if (!worksheet['!merges']) {
        worksheet['!merges'] = [];
    }
    worksheet['!merges'].push(titleMerge);

    // 应用数据合并（基于mergeInfo）
    if (data.mergeInfo && data.mergeInfo.length > 0) {
        data.mergeInfo.forEach(merge => {
            // 调整行号（因为添加了标题行）
            const adjustedMerge = {
                s: { r: merge.startRow + 2, c: merge.startCol }, // +2 因为有主标题和列标题
                e: { r: merge.endRow + 2, c: merge.endCol }
            };
            worksheet['!merges'].push(adjustedMerge);
        });
    }

    // 应用单元格样式（注意：SheetJS免费版对样式支持有限）
    applyCellStyles(worksheet, range, numCols);
}

// 获取列宽设置
// 根据任务9要求：日期:15, 维护保养情况:25, 故障处理情况:50, 空仓？:15, 备注:15
function getColumnWidths(headers) {
    const defaultWidths = {
        '日期': 15,
        '维护保养情况': 25,
        '故障处理情况': 50,
        '空仓？': 15,
        '空仓': 15,  // 兼容不同的列名变体
        '备注': 15
    };

    return headers.map(header => {
        const width = defaultWidths[header] || 20; // 未知列默认20字符宽度
        return { wch: width };
    });
}

// 应用单元格样式
function applyCellStyles(worksheet, range, numCols) {
    // 定义边框样式
    const borderStyle = {
        top: { style: 'thin', color: { rgb: '000000' } },
        bottom: { style: 'thin', color: { rgb: '000000' } },
        left: { style: 'thin', color: { rgb: '000000' } },
        right: { style: 'thin', color: { rgb: '000000' } }
    };

    // 定义样式模板
    const styles = {
        mainTitle: {
            font: {
                name: '宋体',
                size: 14,
                bold: true,
                color: { rgb: '000000' }
            },
            alignment: {
                horizontal: 'center',
                vertical: 'center'
            }
        },
        columnHeader: {
            font: {
                name: '宋体',
                size: 12,
                bold: true,
                color: { rgb: 'FFFFFF' }
            },
            fill: {
                patternType: 'solid',
                fgColor: { rgb: '4472C4' }
            },
            alignment: {
                horizontal: 'center',
                vertical: 'center'
            },
            border: borderStyle
        },
        dataCell: {
            font: {
                name: '宋体',
                size: 11,
                color: { rgb: '000000' }
            },
            alignment: {
                horizontal: 'center',  // 任务9要求：其他列居中对齐
                vertical: 'center',
                wrapText: true         // 任务9要求：启用自动换行功能
            },
            border: borderStyle
        },
        dataCellLeft: {
            font: {
                name: '宋体',
                size: 11,
                color: { rgb: '000000' }
            },
            alignment: {
                horizontal: 'left',    // 任务9要求：故障处理情况列左对齐
                vertical: 'center',
                wrapText: true         // 任务9要求：启用自动换行功能
            },
            border: borderStyle
        },
        dateCell: {
            font: {
                name: '宋体',
                size: 11,
                color: { rgb: '000000' }
            },
            alignment: {
                horizontal: 'center',
                vertical: 'center',
                wrapText: true
            },
            border: borderStyle,
            numFmt: 'yyyy-mm-dd'  // 设置日期格式，只显示年月日
        }
    };

    try {
        // 获取故障处理情况列和日期列的索引
        const failureColumnIndex = getFailureColumnIndex(worksheet, numCols);
        const dateColumnIndex = getDateColumnIndex(worksheet, numCols);

        for (let R = range.s.r; R <= range.e.r; ++R) {
            for (let C = range.s.c; C <= range.e.c; ++C) {
                const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });

                // 确保单元格存在
                if (!worksheet[cellAddress]) {
                    worksheet[cellAddress] = { t: 's', v: '' };
                }

                // 应用样式
                if (R === 0) {
                    // 主标题行
                    worksheet[cellAddress].s = JSON.parse(JSON.stringify(styles.mainTitle));
                } else if (R === 1) {
                    // 列标题行
                    worksheet[cellAddress].s = JSON.parse(JSON.stringify(styles.columnHeader));
                } else {
                    // 数据行
                    if (C === failureColumnIndex) {
                        // 故障处理情况列 - 左对齐
                        worksheet[cellAddress].s = JSON.parse(JSON.stringify(styles.dataCellLeft));
                    } else if (C === dateColumnIndex) {
                        // 日期列 - 应用日期格式
                        worksheet[cellAddress].s = JSON.parse(JSON.stringify(styles.dateCell));

                        // 确保日期单元格的类型和值正确
                        const cellValue = worksheet[cellAddress].v;
                        console.log('Processing date cell:', cellAddress, 'value:', cellValue, 'type:', typeof cellValue);

                        if (cellValue instanceof Date) {
                            // 如果是Date对象，转换为Excel日期序列号
                            const excelDate = (cellValue.getTime() - new Date(1900, 0, 1).getTime()) / (24 * 60 * 60 * 1000) + 1;
                            // 处理Excel的1900年闰年bug
                            const adjustedExcelDate = excelDate >= 60 ? excelDate + 1 : excelDate;
                            worksheet[cellAddress].v = adjustedExcelDate;
                            worksheet[cellAddress].t = 'n'; // 数字类型，但有日期格式
                            console.log('Date converted to Excel serial:', cellValue, '->', adjustedExcelDate);
                        } else if (typeof cellValue === 'number' && cellValue > 1) {
                            // 如果已经是数字（可能是Excel日期序列号），保持数字类型
                            worksheet[cellAddress].t = 'n';
                            console.log('Keeping numeric date value:', cellValue);
                        } else if (cellValue) {
                            // 尝试解析其他类型的日期值
                            try {
                                const parsedDate = new Date(cellValue);
                                if (!isNaN(parsedDate.getTime())) {
                                    const excelDate = (parsedDate.getTime() - new Date(1900, 0, 1).getTime()) / (24 * 60 * 60 * 1000) + 1;
                                    const adjustedExcelDate = excelDate >= 60 ? excelDate + 1 : excelDate;
                                    worksheet[cellAddress].v = adjustedExcelDate;
                                    worksheet[cellAddress].t = 'n';
                                    console.log('Parsed and converted date:', cellValue, '->', adjustedExcelDate);
                                }
                            } catch (error) {
                                console.warn('Failed to parse date value:', cellValue, error);
                            }
                        }
                    } else {
                        // 其他数据列 - 居中对齐
                        worksheet[cellAddress].s = JSON.parse(JSON.stringify(styles.dataCell));
                    }
                }
            }
        }
    } catch (error) {
        console.warn('样式设置失败，使用默认样式:', error);
        // 如果样式设置失败，至少保证基本的对齐方式
        applyBasicStyles(worksheet, range, numCols);
    }
}

// 备用的基本样式设置函数
function applyBasicStyles(worksheet, range, numCols) {
    try {
        const failureColumnIndex = getFailureColumnIndex(worksheet, numCols);

        for (let R = range.s.r; R <= range.e.r; ++R) {
            for (let C = range.s.c; C <= range.e.c; ++C) {
                const cellAddress = XLSX.utils.encode_cell({ r: R, c: C });

                if (!worksheet[cellAddress]) {
                    worksheet[cellAddress] = { t: 's', v: '' };
                }

                if (!worksheet[cellAddress].s) worksheet[cellAddress].s = {};

                if (R === 0) {
                    // 主标题行
                    worksheet[cellAddress].s.alignment = { horizontal: 'center', vertical: 'center' };
                    worksheet[cellAddress].s.font = { bold: true };
                } else if (R === 1) {
                    // 列标题行
                    worksheet[cellAddress].s.alignment = { horizontal: 'center', vertical: 'center' };
                    worksheet[cellAddress].s.font = { bold: true };
                } else {
                    // 数据行
                    worksheet[cellAddress].s.alignment = {
                        horizontal: C === failureColumnIndex ? 'left' : 'center',
                        vertical: 'center',
                        wrapText: true
                    };
                }
            }
        }
    } catch (error) {
        console.warn('基本样式设置也失败:', error);
    }
}

// 获取故障处理情况列的索引
function getFailureColumnIndex(worksheet, numCols) {
    // 查找故障处理情况列
    for (let C = 0; C < numCols; C++) {
        const cellAddress = XLSX.utils.encode_cell({ r: 1, c: C });
        const cell = worksheet[cellAddress];
        if (cell && cell.v && String(cell.v).includes('故障')) {
            return C;
        }
    }
    return -1;
}

// 获取日期列的索引
function getDateColumnIndex(worksheet, numCols) {
    const dateKeywords = ['日期', 'date', '时间', 'time', '日', '月', '年'];

    // 查找日期列
    for (let C = 0; C < numCols; C++) {
        const cellAddress = XLSX.utils.encode_cell({ r: 1, c: C });
        const cell = worksheet[cellAddress];
        if (cell && cell.v) {
            const header = String(cell.v).toLowerCase();
            if (dateKeywords.some(keyword => header.includes(keyword))) {
                return C;
            }
        }
    }
    return -1;
}

// 下载工作簿
// 任务10：文件自动命名功能 - 格式：科陆流水线运维日志YYYYMMDD.xlsx
function downloadWorkbook(workbook) {
    try {
        // 生成文件名 - 使用当前日期作为时间戳
        const today = new Date();
        const dateStr = today.getFullYear() +
                       String(today.getMonth() + 1).padStart(2, '0') +
                       String(today.getDate()).padStart(2, '0');
        const fileName = `科陆流水线运维日志${dateStr}.xlsx`;

        // 生成文件并下载
        XLSX.writeFile(workbook, fileName);
        console.log(`文件下载成功: ${fileName}`);
    } catch (error) {
        console.error('文件下载失败:', error);
        throw error;
    }
}

// 显示消息
function showMessage(text, type = 'info') {
    messageArea.textContent = text;
    messageArea.className = `message ${type}`;
    messageArea.style.display = 'block';
    
    // 3秒后自动隐藏成功消息
    if (type === 'success') {
        setTimeout(() => {
            messageArea.style.display = 'none';
        }, 3000);
    }
}

// 显示/隐藏进度
function showProgress(show) {
    progressSection.style.display = show ? 'block' : 'none';
    if (!show) {
        progressFill.style.width = '0%';
    }
}

// 更新进度
function updateProgress(percent, text) {
    progressFill.style.width = percent + '%';
    progressText.textContent = text;
}

// ===== ExcelJS 相关函数 =====

// 使用ExcelJS创建格式化的工作簿
async function createFormattedWorkbookWithExcelJS(data) {
    // 调试：统计接收到的数据中的"空仓运维"数量
    const emptyColumnIndex = data.headers.findIndex(header =>
        header.includes('空仓') || header.includes('空仓？')
    );
    if (emptyColumnIndex !== -1) {
        const emptyMaintenanceCount = data.rows.filter(row =>
            row[emptyColumnIndex] && String(row[emptyColumnIndex]).trim() === '空仓运维'
        ).length;
        console.log(`=== Excel生成开始统计 ===`);
        console.log(`Excel生成时接收到的"空仓运维"数量: ${emptyMaintenanceCount}`);
        console.log(`总数据行数: ${data.rows.length}`);
        console.log(`空仓列索引: ${emptyColumnIndex}`);
    }

    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('运维日志');

    // 添加主标题行 - 确保标题为"科陆流水线日常运维及故障处理情况"
    const titleRow = worksheet.addRow(['科陆流水线日常运维及故障处理情况']);

    // 合并主标题行 - 跨所有列
    worksheet.mergeCells(1, 1, 1, data.headers.length);

    // 设置主标题样式 - 14号宋体加粗，居中对齐
    const titleCell = worksheet.getCell(1, 1);
    titleCell.font = {
        name: '宋体',
        size: 14,
        bold: true,
        color: { argb: 'FF000000' }
    };
    titleCell.alignment = {
        horizontal: 'center',
        vertical: 'middle'
    };

    // 添加列标题行 - 直接使用输入表格的列标题（经过处理后的headers）
    const headerRow = worksheet.addRow(data.headers);

    // 设置列标题样式 - 12号宋体加粗白色字体，蓝色背景
    headerRow.eachCell((cell, colNumber) => {
        cell.font = {
            name: '宋体',
            size: 12,
            bold: true,
            color: { argb: 'FFFFFFFF' }  // 白色字体
        };
        cell.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'FF4472C4' }  // 蓝色背景
        };
        cell.alignment = {
            horizontal: 'center',
            vertical: 'middle'
        };
        cell.border = {
            top: { style: 'thin', color: { argb: 'FF000000' } },
            left: { style: 'thin', color: { argb: 'FF000000' } },
            bottom: { style: 'thin', color: { argb: 'FF000000' } },
            right: { style: 'thin', color: { argb: 'FF000000' } }
        };
    });

    // 添加数据行
    data.rows.forEach(rowData => {
        const dataRow = worksheet.addRow(rowData);

        // 设置数据行样式 - 11号宋体，居中对齐，完整边框
        dataRow.eachCell((cell, colNumber) => {
            cell.font = {
                name: '宋体',
                size: 11,
                color: { argb: 'FF000000' }
            };

            // 根据列类型设置对齐方式
            const columnHeader = data.headers[colNumber - 1];
            if (columnHeader && columnHeader.includes('故障处理情况')) {
                // 故障处理情况列左对齐
                cell.alignment = {
                    horizontal: 'left',
                    vertical: 'middle',
                    wrapText: true
                };
            } else {
                // 其他列居中对齐
                cell.alignment = {
                    horizontal: 'center',
                    vertical: 'middle',
                    wrapText: true
                };
            }

            // 完整边框
            cell.border = {
                top: { style: 'thin', color: { argb: 'FF000000' } },
                left: { style: 'thin', color: { argb: 'FF000000' } },
                bottom: { style: 'thin', color: { argb: 'FF000000' } },
                right: { style: 'thin', color: { argb: 'FF000000' } }
            };

            // 处理日期格式
            if (columnHeader && columnHeader.includes('日期') && cell.value instanceof Date) {
                cell.numFmt = 'yyyy-mm-dd';
            }
        });
    });

    // 设置列宽
    const columnWidths = getColumnWidthsForExcelJS(data.headers);
    columnWidths.forEach((width, index) => {
        worksheet.getColumn(index + 1).width = width;
    });

    // 设置行高
    worksheet.eachRow((row, rowNumber) => {
        row.height = 25; // 25像素行高
    });

    // 应用单元格合并
    if (data.mergeInfo && data.mergeInfo.length > 0) {
        console.log(`=== 开始应用单元格合并 ===`);
        console.log(`合并信息数量: ${data.mergeInfo.length}`);

        // 在合并前统计"空仓运维"数量
        if (emptyColumnIndex !== -1) {
            let emptyMaintenanceCountBeforeMerge = 0;
            for (let i = 0; i < data.rows.length; i++) {
                const row = data.rows[i];
                if (row[emptyColumnIndex] && String(row[emptyColumnIndex]).trim() === '空仓运维') {
                    emptyMaintenanceCountBeforeMerge++;
                    console.log(`合并前发现"空仓运维"在行${i + 3}: "${row[emptyColumnIndex]}"`);
                }
            }
            console.log(`合并前"空仓运维"总数: ${emptyMaintenanceCountBeforeMerge}`);
        }

        data.mergeInfo.forEach((merge, index) => {
            // 调整行号（因为添加了标题行）
            const startRow = merge.startRow + 3; // +2 因为有主标题和列标题，+1 因为ExcelJS从1开始
            const endRow = merge.endRow + 3;
            const startCol = merge.startCol + 1; // ExcelJS从1开始
            const endCol = merge.endCol + 1;

            // 检查是否涉及空仓列的合并
            if (emptyColumnIndex !== -1 && startCol <= emptyColumnIndex + 1 && endCol >= emptyColumnIndex + 1) {
                console.log(`警告: 合并${index + 1}涉及空仓列! 行${startRow}-${endRow}, 列${startCol}-${endCol}`);

                // 收集被合并区域内的"空仓运维"数据
                const emptyMaintenanceValues = [];
                for (let row = startRow; row <= endRow; row++) {
                    const dataRowIndex = row - 3; // 转换回数据行索引
                    if (dataRowIndex >= 0 && dataRowIndex < data.rows.length) {
                        const cellValue = data.rows[dataRowIndex][emptyColumnIndex];
                        if (cellValue && String(cellValue).trim() === '空仓运维') {
                            console.log(`  发现"空仓运维"在被合并行${row}(数据行${dataRowIndex}): "${cellValue}"`);
                            emptyMaintenanceValues.push(cellValue);
                        }
                    }
                }

                // 如果涉及空仓列且有"空仓运维"数据，跳过此合并
                if (emptyMaintenanceValues.length > 0) {
                    console.log(`  跳过合并${index + 1}，因为会丢失${emptyMaintenanceValues.length}个"空仓运维"数据`);
                    return; // 跳过这个合并
                }
            }

            try {
                worksheet.mergeCells(startRow, startCol, endRow, endCol);
            } catch (error) {
                console.warn('合并单元格失败:', error, merge);
            }
        });

        // 合并后再次统计"空仓运维"数量
        if (emptyColumnIndex !== -1) {
            let emptyMaintenanceCountAfterMerge = 0;
            for (let rowNum = 3; rowNum <= data.rows.length + 2; rowNum++) {
                const cell = worksheet.getCell(rowNum, emptyColumnIndex + 1);
                if (cell.value && String(cell.value).trim() === '空仓运维') {
                    emptyMaintenanceCountAfterMerge++;
                    console.log(`合并后发现"空仓运维"在Excel行${rowNum}: "${cell.value}"`);
                }
            }
            console.log(`合并后"空仓运维"总数: ${emptyMaintenanceCountAfterMerge}`);
        }

        console.log(`=== 单元格合并完成 ===`);
    }

    return workbook;
}

// 获取ExcelJS的列宽设置
function getColumnWidthsForExcelJS(headers) {
    const defaultWidth = 15;
    const columnWidthMap = {
        '日期': 15,
        '维护保养情况': 25,
        '故障处理情况': 50,
        '空仓？': 15,
        '空仓': 15,
        '备注': 15
    };

    return headers.map(header => {
        for (const [keyword, width] of Object.entries(columnWidthMap)) {
            if (header.includes(keyword)) {
                return width;
            }
        }
        return defaultWidth;
    });
}

// 使用ExcelJS下载工作簿
async function downloadWorkbookWithExcelJS(workbook) {
    try {
        // 生成文件名 - 使用当前日期作为时间戳
        const today = new Date();
        const dateStr = today.getFullYear() +
                       String(today.getMonth() + 1).padStart(2, '0') +
                       String(today.getDate()).padStart(2, '0');
        const fileName = `科陆流水线运维日志${dateStr}.xlsx`;

        // 生成文件buffer
        const buffer = await workbook.xlsx.writeBuffer();

        // 创建Blob并下载
        const blob = new Blob([buffer], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });

        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        console.log(`文件下载成功: ${fileName}`);
    } catch (error) {
        console.error('文件下载失败:', error);
        throw error;
    }
}

// ==================== 月视图功能 ====================

// 分析日期分布
function analyzeDateDistribution(processedData) {
    if (!processedData || !processedData.headers || !processedData.rows) {
        console.warn('无法分析日期分布：数据不完整');
        return;
    }

    // 找到日期列索引
    const dateColumnIndex = findDateColumnIndex(processedData.headers);
    if (dateColumnIndex === -1) {
        console.warn('未找到日期列，无法分析日期分布');
        return;
    }

    // 统计日期分布
    const dateCount = new Map();
    const allDates = [];

    processedData.rows.forEach(row => {
        const dateValue = row[dateColumnIndex];
        if (dateValue) {
            const formattedDate = formatDateValue(dateValue);
            if (formattedDate instanceof Date) {
                const dateKey = getDateGroupKey(dateValue);
                dateCount.set(dateKey, (dateCount.get(dateKey) || 0) + 1);
                allDates.push(formattedDate);
            }
        }
    });

    // 计算日期范围
    let minDate = null;
    let maxDate = null;
    if (allDates.length > 0) {
        minDate = new Date(Math.min(...allDates));
        maxDate = new Date(Math.max(...allDates));
    }

    dateDistribution = {
        dateCount,
        totalRecords: processedData.rows.length,
        dateRange: { min: minDate, max: maxDate },
        dateColumnIndex
    };

    console.log('日期分布分析完成:', dateDistribution);
}

// 显示月视图
function showMonthView() {
    if (!dateDistribution) {
        console.warn('没有日期分布数据，无法显示月视图');
        return;
    }

    // 显示月视图区域
    monthViewSection.style.display = 'block';

    // 更新统计信息
    updateDateStats();

    // 设置当前月份为数据范围内的第一个月
    if (dateDistribution.dateRange.min) {
        currentCalendarDate = new Date(dateDistribution.dateRange.min);
    }

    // 渲染月历
    renderCalendar();

    // 更新选中日期显示
    updateSelectedDatesDisplay();
}

// 更新日期统计信息
function updateDateStats() {
    if (!dateDistribution) return;

    totalRecords.textContent = dateDistribution.totalRecords;

    if (dateDistribution.dateRange.min && dateDistribution.dateRange.max) {
        const minStr = dateDistribution.dateRange.min.toLocaleDateString('zh-CN');
        const maxStr = dateDistribution.dateRange.max.toLocaleDateString('zh-CN');
        dateRange.textContent = `${minStr} - ${maxStr}`;
    } else {
        dateRange.textContent = '无日期数据';
    }

    selectedCount.textContent = selectedDates.size;
}

// 渲染月历
function renderCalendar() {
    if (!calendarContainer) return;

    const year = currentCalendarDate.getFullYear();
    const month = currentCalendarDate.getMonth();

    // 更新月份显示
    currentMonth.textContent = `${year}年${month + 1}月`;

    // 创建月历表格
    const table = document.createElement('table');
    table.className = 'calendar-table';

    // 创建表头（星期）
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');
    const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
    weekdays.forEach(day => {
        const th = document.createElement('th');
        th.textContent = day;
        headerRow.appendChild(th);
    });
    thead.appendChild(headerRow);
    table.appendChild(thead);

    // 创建表体（日期）
    const tbody = document.createElement('tbody');

    // 获取当月第一天和最后一天
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const firstDayWeek = firstDay.getDay();
    const daysInMonth = lastDay.getDate();

    // 计算需要显示的周数
    const totalCells = Math.ceil((firstDayWeek + daysInMonth) / 7) * 7;

    let currentDate = 1;
    let nextMonthDate = 1;

    for (let i = 0; i < totalCells; i += 7) {
        const row = document.createElement('tr');

        for (let j = 0; j < 7; j++) {
            const cell = document.createElement('td');
            const dateDiv = document.createElement('div');
            dateDiv.className = 'calendar-date';

            const cellIndex = i + j;

            if (cellIndex < firstDayWeek) {
                // 上个月的日期
                const prevMonth = new Date(year, month - 1, 0);
                const prevDate = prevMonth.getDate() - (firstDayWeek - cellIndex - 1);
                dateDiv.textContent = prevDate;
                dateDiv.classList.add('other-month');
            } else if (currentDate <= daysInMonth) {
                // 当前月的日期
                dateDiv.textContent = currentDate;
                const dateKey = `${year}-${String(month + 1).padStart(2, '0')}-${String(currentDate).padStart(2, '0')}`;

                // 检查是否有数据
                if (dateDistribution.dateCount.has(dateKey)) {
                    dateDiv.classList.add('has-data');

                    // 添加数据密度指示器
                    const density = document.createElement('div');
                    density.className = 'data-density';
                    const count = dateDistribution.dateCount.get(dateKey);
                    if (count > 10) {
                        density.classList.add('high');
                    } else if (count > 5) {
                        density.classList.add('medium');
                    } else {
                        density.classList.add('low');
                    }
                    dateDiv.appendChild(density);
                }

                // 检查是否已选中
                if (selectedDates.has(dateKey)) {
                    dateDiv.classList.add('selected');
                }

                // 添加点击事件
                dateDiv.addEventListener('click', () => toggleDateSelection(dateKey));

                currentDate++;
            } else {
                // 下个月的日期
                dateDiv.textContent = nextMonthDate;
                dateDiv.classList.add('other-month');
                nextMonthDate++;
            }

            cell.appendChild(dateDiv);
            row.appendChild(cell);
        }

        tbody.appendChild(row);
    }

    table.appendChild(tbody);

    // 清空容器并添加新表格
    calendarContainer.innerHTML = '';
    calendarContainer.appendChild(table);
}

// 切换日期选择状态
function toggleDateSelection(dateKey) {
    if (selectedDates.has(dateKey)) {
        selectedDates.delete(dateKey);
    } else {
        selectedDates.add(dateKey);
    }

    // 更新显示
    updateSelectedDatesDisplay();
    updateDateStats();
    renderCalendar(); // 重新渲染以更新选中状态

    // 更新导出按钮状态
    exportFilteredBtn.disabled = selectedDates.size === 0;
}

// 更新选中日期显示
function updateSelectedDatesDisplay() {
    if (!selectedDatesList) return;

    if (selectedDates.size === 0) {
        selectedDatesList.innerHTML = '<p class="no-selection">未选择任何日期</p>';
        return;
    }

    // 将选中的日期排序
    const sortedDates = Array.from(selectedDates).sort();

    selectedDatesList.innerHTML = '';
    sortedDates.forEach(dateKey => {
        const dateItem = document.createElement('span');
        dateItem.className = 'selected-date-item';

        const dateText = document.createElement('span');
        dateText.textContent = dateKey;

        const removeBtn = document.createElement('span');
        removeBtn.className = 'remove-date';
        removeBtn.textContent = '×';
        removeBtn.addEventListener('click', () => {
            selectedDates.delete(dateKey);
            updateSelectedDatesDisplay();
            updateDateStats();
            renderCalendar();
            exportFilteredBtn.disabled = selectedDates.size === 0;
        });

        dateItem.appendChild(dateText);
        dateItem.appendChild(removeBtn);
        selectedDatesList.appendChild(dateItem);
    });
}

// 显示上一个月
function showPreviousMonth() {
    currentCalendarDate.setMonth(currentCalendarDate.getMonth() - 1);
    renderCalendar();
}

// 显示下一个月
function showNextMonth() {
    currentCalendarDate.setMonth(currentCalendarDate.getMonth() + 1);
    renderCalendar();
}

// 清除日期选择
function clearDateSelection() {
    selectedDates.clear();
    updateSelectedDatesDisplay();
    updateDateStats();
    renderCalendar();
    exportFilteredBtn.disabled = true;
}

// 预览过滤数据
function previewFilteredData() {
    if (!currentWorkbookData || selectedDates.size === 0) {
        showMessage('请先选择要预览的日期', 'error');
        return;
    }

    const filteredData = filterDataBySelectedDates();

    // 创建预览窗口
    const previewWindow = window.open('', '_blank', 'width=800,height=600');
    previewWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
            <title>数据预览</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                .info { background: #e7f3ff; padding: 10px; margin-bottom: 20px; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class="info">
                <h2>过滤数据预览</h2>
                <p>选中日期: ${Array.from(selectedDates).sort().join(', ')}</p>
                <p>过滤后记录数: ${filteredData.rows.length}</p>
            </div>
            <table>
                <thead>
                    <tr>
                        ${filteredData.headers.map(header => `<th>${header}</th>`).join('')}
                    </tr>
                </thead>
                <tbody>
                    ${filteredData.rows.map(row =>
                        `<tr>${row.map(cell => `<td>${cell || ''}</td>`).join('')}</tr>`
                    ).join('')}
                </tbody>
            </table>
        </body>
        </html>
    `);
    previewWindow.document.close();
}

// 导出过滤数据
async function exportFilteredData() {
    if (!currentWorkbookData || selectedDates.size === 0) {
        showMessage('请先选择要导出的日期', 'error');
        return;
    }

    try {
        showMessage('正在生成过滤数据...', 'info');

        const filteredData = filterDataBySelectedDates();

        // 生成新的Excel文件
        const newWorkbook = await createFormattedWorkbookWithExcelJS(filteredData);

        // 生成文件名
        const today = new Date();
        const dateStr = today.getFullYear() +
                       String(today.getMonth() + 1).padStart(2, '0') +
                       String(today.getDate()).padStart(2, '0');
        const fileName = `科陆流水线运维日志_筛选_${dateStr}.xlsx`;

        // 生成文件buffer
        const buffer = await newWorkbook.xlsx.writeBuffer();

        // 创建Blob并下载
        const blob = new Blob([buffer], {
            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        });

        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);

        showMessage(`过滤数据导出成功！文件名: ${fileName}`, 'success');

    } catch (error) {
        console.error('导出过滤数据失败:', error);
        showMessage('导出失败: ' + error.message, 'error');
    }
}

// 根据选中日期过滤数据
function filterDataBySelectedDates() {
    if (!currentWorkbookData || !dateDistribution) {
        return { headers: [], rows: [] };
    }

    const dateColumnIndex = dateDistribution.dateColumnIndex;
    const filteredRows = [];

    currentWorkbookData.rows.forEach(row => {
        const dateValue = row[dateColumnIndex];
        if (dateValue) {
            const dateKey = getDateGroupKey(dateValue);
            if (selectedDates.has(dateKey)) {
                filteredRows.push([...row]); // 复制行数据
            }
        }
    });

    return {
        headers: [...currentWorkbookData.headers], // 复制表头
        rows: filteredRows,
        mergeInfo: [] // 过滤后的数据不需要合并信息
    };
}
