/* 基础样式重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
.header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* 主内容区域 */
.main-content {
    flex: 1;
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* 文件上传区域 */
.upload-section {
    margin-bottom: 30px;
}

.upload-area {
    border: 3px dashed #667eea;
    border-radius: 10px;
    padding: 40px 20px;
    text-align: center;
    background: #f8f9ff;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.upload-area:hover {
    border-color: #5a67d8;
    background: #f0f2ff;
    transform: translateY(-2px);
}

.upload-area.dragover {
    border-color: #4c51bf;
    background: #e6f3ff;
}

.upload-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.upload-area h3 {
    color: #667eea;
    margin-bottom: 10px;
    font-size: 1.3rem;
}

.upload-area p {
    color: #666;
    margin-bottom: 20px;
}

.file-input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    cursor: pointer;
    pointer-events: none;
}

.upload-btn {
    background: #667eea;
    color: white;
    border: none;
    padding: 12px 30px;
    border-radius: 25px;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.upload-btn:hover {
    background: #5a67d8;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

/* 文件信息显示 */
.file-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-top: 20px;
    border-left: 4px solid #667eea;
}

.file-info h4 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    padding: 5px 0;
}

.info-item .label {
    font-weight: 600;
    color: #555;
}

.info-item .value {
    color: #333;
}

/* 转换控制区域 */
.control-section {
    text-align: center;
    margin-bottom: 30px;
}

.convert-btn {
    background: linear-gradient(45deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 15px 40px;
    border-radius: 30px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.convert-btn:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.convert-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    box-shadow: none;
}

/* 进度指示器 */
.progress-section {
    margin-top: 20px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(45deg, #667eea, #764ba2);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    color: #667eea;
    font-weight: 600;
    text-align: center;
}

/* 消息区域 */
.message-section {
    min-height: 50px;
}

.message {
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 10px;
    display: none;
}

.message.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
    display: block;
}

.message.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
    display: block;
}

.message.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
    display: block;
}

/* 页脚 */
.footer {
    text-align: center;
    margin-top: 30px;
    color: white;
    opacity: 0.8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .main-content {
        padding: 20px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .upload-area {
        padding: 30px 15px;
    }

    .convert-btn {
        padding: 12px 30px;
        font-size: 1rem;
    }

    /* 月视图响应式 */
    .month-view-section {
        padding: 15px;
    }

    .date-stats {
        flex-direction: column;
        gap: 10px;
    }

    .stats-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .stats-label,
    .stats-value {
        display: inline;
    }

    .calendar-navigation {
        flex-wrap: wrap;
        gap: 10px;
    }

    .current-month {
        min-width: auto;
        order: -1;
        width: 100%;
    }

    .calendar-table th,
    .calendar-table td {
        height: 35px;
        font-size: 0.8rem;
    }

    .calendar-date {
        height: 30px;
    }

    .month-view-actions {
        flex-direction: column;
        gap: 10px;
    }

    .action-btn {
        width: 100%;
    }

    .selected-dates-list {
        max-height: 100px;
    }
}

/* 月视图样式 */
.month-view-section {
    margin-top: 30px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 12px;
    border: 1px solid #e9ecef;
}

.month-view-section h3 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.3rem;
    text-align: center;
}

.month-view-container {
    max-width: 100%;
}

/* 日期统计信息 */
.date-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 20px;
    padding: 15px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stats-item {
    text-align: center;
}

.stats-label {
    display: block;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 5px;
}

.stats-value {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
}

/* 月历导航 */
.calendar-navigation {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
    gap: 15px;
}

.nav-btn {
    background: #667eea;
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 1.2rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.nav-btn:hover {
    background: #5a67d8;
    transform: scale(1.1);
}

.current-month {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    min-width: 120px;
    text-align: center;
}

.clear-selection-btn {
    background: #dc3545;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.clear-selection-btn:hover {
    background: #c82333;
}

/* 月历容器 */
.calendar-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

/* 月历表格 */
.calendar-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.calendar-table th,
.calendar-table td {
    width: 14.28%;
    height: 45px;
    text-align: center;
    vertical-align: middle;
    border: 1px solid #e9ecef;
    position: relative;
}

.calendar-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.calendar-date {
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 4px;
    margin: 2px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.calendar-date:hover {
    background: #e3f2fd;
}

.calendar-date.other-month {
    color: #ccc;
    cursor: default;
}

.calendar-date.other-month:hover {
    background: transparent;
}

.calendar-date.has-data {
    background: #e8f5e8;
    font-weight: 600;
}

.calendar-date.has-data:hover {
    background: #d4edda;
}

.calendar-date.selected {
    background: #667eea !important;
    color: white;
}

.calendar-date.selected:hover {
    background: #5a67d8 !important;
}

/* 数据密度指示器 */
.data-density {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: #28a745;
}

.data-density.high {
    background: #dc3545;
}

.data-density.medium {
    background: #ffc107;
}

.data-density.low {
    background: #28a745;
}

/* 选中日期列表 */
.selected-dates {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.selected-dates h4 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.selected-dates-list {
    max-height: 150px;
    overflow-y: auto;
}

.no-selection {
    color: #999;
    font-style: italic;
    text-align: center;
    margin: 0;
}

.selected-date-item {
    display: inline-block;
    background: #667eea;
    color: white;
    padding: 5px 12px;
    margin: 3px;
    border-radius: 15px;
    font-size: 0.9rem;
    position: relative;
}

.selected-date-item .remove-date {
    margin-left: 8px;
    cursor: pointer;
    font-weight: bold;
}

.selected-date-item .remove-date:hover {
    color: #ffcccb;
}

/* 操作按钮 */
.month-view-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.action-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.preview-btn {
    background: #17a2b8;
    color: white;
}

.preview-btn:hover {
    background: #138496;
    transform: translateY(-2px);
}

.export-btn {
    background: #28a745;
    color: white;
}

.export-btn:hover:not(:disabled) {
    background: #218838;
    transform: translateY(-2px);
}

.export-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.file-info {
    animation: fadeIn 0.5s ease;
}

.progress-section {
    animation: fadeIn 0.5s ease;
}

.month-view-section {
    animation: fadeIn 0.5s ease;
}
