<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Excel转换器 - 科陆流水线运维日志</title>
    <link rel="stylesheet" href="styles.css">
    <!-- 引入ExcelJS库 -->
    <script src="https://cdn.jsdelivr.net/npm/exceljs@4.4.0/dist/exceljs.min.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>Excel转换器</h1>
            <p class="subtitle">科陆流水线运维日志格式转换工具</p>
        </header>

        <main class="main-content">
            <!-- 文件上传区域 -->
            <div class="upload-section">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-icon">📁</div>
                    <h3>选择Excel文件</h3>
                    <p>支持 .xlsx 格式文件</p>
                    <input type="file" id="fileInput" accept=".xlsx" class="file-input">
                    <button type="button" class="upload-btn" onclick="document.getElementById('fileInput').click()">
                        选择文件
                    </button>
                </div>
                
                <!-- 文件信息显示 -->
                <div class="file-info" id="fileInfo" style="display: none;">
                    <h4>文件信息</h4>
                    <div class="info-item">
                        <span class="label">文件名:</span>
                        <span class="value" id="fileName"></span>
                    </div>
                    <div class="info-item">
                        <span class="label">文件大小:</span>
                        <span class="value" id="fileSize"></span>
                    </div>
                    <div class="info-item">
                        <span class="label">最后修改:</span>
                        <span class="value" id="fileDate"></span>
                    </div>
                </div>
            </div>

            <!-- 转换控制区域 -->
            <div class="control-section">
                <button id="convertBtn" class="convert-btn" disabled>
                    开始转换
                </button>
                
                <!-- 进度指示器 -->
                <div class="progress-section" id="progressSection" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">准备中...</div>
                </div>
            </div>

            <!-- 月视图区域 -->
            <div class="month-view-section" id="monthViewSection" style="display: none;">
                <h3>日期分布月视图</h3>
                <div class="month-view-container">
                    <!-- 日期统计信息 -->
                    <div class="date-stats">
                        <div class="stats-item">
                            <span class="stats-label">总记录数:</span>
                            <span class="stats-value" id="totalRecords">0</span>
                        </div>
                        <div class="stats-item">
                            <span class="stats-label">日期范围:</span>
                            <span class="stats-value" id="dateRange">-</span>
                        </div>
                        <div class="stats-item">
                            <span class="stats-label">已选择:</span>
                            <span class="stats-value" id="selectedCount">0</span>
                        </div>
                    </div>

                    <!-- 月历导航 -->
                    <div class="calendar-navigation">
                        <button class="nav-btn" id="prevMonth">‹</button>
                        <span class="current-month" id="currentMonth">2024年1月</span>
                        <button class="nav-btn" id="nextMonth">›</button>
                        <button class="clear-selection-btn" id="clearSelection">清除选择</button>
                    </div>

                    <!-- 月历容器 -->
                    <div class="calendar-container" id="calendarContainer">
                        <!-- 月历将通过JavaScript动态生成 -->
                    </div>

                    <!-- 选中日期列表 -->
                    <div class="selected-dates">
                        <h4>已选择的日期</h4>
                        <div class="selected-dates-list" id="selectedDatesList">
                            <p class="no-selection">未选择任何日期</p>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="month-view-actions">
                        <button class="action-btn preview-btn" id="previewData">预览过滤数据</button>
                        <button class="action-btn export-btn" id="exportFiltered" disabled>导出选中数据</button>
                    </div>
                </div>
            </div>

            <!-- 状态消息区域 -->
            <div class="message-section">
                <div class="message" id="messageArea"></div>
            </div>
        </main>

        <footer class="footer">
            <p>&copy; 2024 Excel转换器 - 科陆流水线运维工具</p>
        </footer>
    </div>

    <script src="script.js"></script>
</body>
</html>
